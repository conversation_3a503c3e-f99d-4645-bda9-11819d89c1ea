from pydantic import Field

from core.schemas import CustomModel
from schemas.confirmed_data import ConfirmedData
from schemas.extracted_data import AggregatedData
from schemas.ldmf_countries import CountryData
from schemas.quals_clients import ClientSearchItem


__all__ = ['CombinedExtractedDataResponse', 'DateIntervalsObject']


class DateIntervalsObject(CustomModel):
    """Schema for date intervals as an object."""

    start_date: str | None = None
    end_date: str | None = None


class CombinedExtractedDataResponse(CustomModel):
    """
    Combined schema for extracted and aggregated data.
    Flattened structure with fields from confirmed data at root level.
    """

    # Core fields from ConfirmedData
    client_name: ClientSearchItem | None = None
    ldmf_country: CountryData | None = None
    date_intervals: DateIntervalsObject | None = None
    objective_and_scope: str | None = None
    outcomes: str | None = None

    # Dash activity ID
    dash_activity_id: int | None = Field(default=None)

    # Fields from AggregatedData, excluding those already in ConfirmedData
    # and those explicitly excluded by the user.
    business_issues: str | None = Field(default=None)
    scope_approach: str | None = Field(default=None)
    value_delivered: str | None = Field(default=None)
    engagement_summary: str | None = Field(default=None)
    one_line_description: str | None = Field(default=None)
    client_references: str | None = Field(default=None)
    client_name_sharing: str | None = Field(default=None)
    client_industry: list[str] = Field(default_factory=list)
    engagement_dates: list[str] = Field(default_factory=list)
    engagement_locations: list[str] = Field(default_factory=list)
    engagement_fee_display: str | None = Field(default=None)
    client_services: list[str] = Field(default_factory=list)
    source_of_work: str | None = Field(default=None)
    qual_usage: str | None = Field(default=None)
    team_roles: str | None = Field(default=None)
    approver: str | None = Field(default=None)

    @classmethod
    async def from_confirmed_and_aggregated_data(
        cls,
        confirmed_data: ConfirmedData,
        aggregated_data: AggregatedData,
        dash_activity_id: int | None = None,
        ldmf_country: CountryData | None = None,
        fetched_client_name: ClientSearchItem | None = None,
    ) -> 'CombinedExtractedDataResponse':
        # Convert date_intervals tuple to DateIntervalsObject if it exists
        date_intervals_obj = None
        if confirmed_data.date_intervals:
            start_date, end_date = confirmed_data.date_intervals
            date_intervals_obj = DateIntervalsObject(start_date=start_date, end_date=end_date)

        # Start with confirmed data fields at root level
        combined_fields = {
            'client_name': fetched_client_name if fetched_client_name is not None else confirmed_data.client_name,
            'ldmf_country': ldmf_country,
            'date_intervals': date_intervals_obj,
            'objective_and_scope': confirmed_data.objective_and_scope,
            'outcomes': confirmed_data.outcomes,
            'dash_activity_id': dash_activity_id,
        }

        excluded_aggregated_fields = {
            'client_name',
            'ldmf_country',
            'date_intervals',
            'date_intervals_original',
            'objective_and_scope',
            'outcomes',
            'more_than_two_dates',
        }

        # Add fields from aggregated data that are not excluded
        for field_name, value in aggregated_data.model_dump().items():
            if field_name not in excluded_aggregated_fields:
                combined_fields[field_name] = value

        return cls.model_validate(combined_fields)
