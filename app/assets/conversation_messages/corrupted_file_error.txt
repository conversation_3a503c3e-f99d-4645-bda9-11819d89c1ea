There was a problem reading {utils_part} ({file_names}). Try uploading the file(s) again after verifying that it: 1) opens without errors, 2) does not have any sensitivity labels, and 3) is not password protected. If the issue continues, please report it <a href="{support_url}" target="_blank"><u>here</u></a>. Alternatively, you may copy the text from the file into the chat or continue to create a qual without this file.
