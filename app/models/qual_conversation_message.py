import sqlalchemy as sa
from sqlalchemy.orm import relationship
from sqlalchemy.schema import Index

from constants.message import ConversationMessageIntention, MessageRole, MessageType, SystemReplyType
from core.db import Base


__all__ = ['QualConversationMessage']


class QualConversationMessage(Base):
    __tablename__ = 'QualConversationMessage'

    Id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    PublicId = sa.Column(sa.Uuid, unique=True, nullable=False, server_default=sa.text('NEWID()'))
    QualConversationId = sa.Column(sa.Integer, sa.<PERSON>ey('QualConversation.Id'), nullable=False)
    Role = sa.Column(sa.Enum(MessageRole, values_callable=lambda x: [e.value for e in x]), nullable=False)
    Type = sa.Column(sa.Enum(MessageType, values_callable=lambda x: [e.value for e in x]), nullable=False)
    Content = sa.Column(sa.UnicodeText, nullable=False)
    Translation = sa.Column(sa.UnicodeText, nullable=True)  # NOTE: For user messages with no options selected only
    Options = sa.Column(sa.UnicodeText, nullable=False)  # NOTE: For system messages only
    SelectedOption = sa.Column(sa.UnicodeText, nullable=True)  # NOTE: For user messages only
    CreatedAt = sa.Column(sa.DateTime, server_default=sa.func.now(), nullable=False)
    Intention = sa.Column(
        sa.Enum(ConversationMessageIntention, values_callable=lambda x: [e.value for e in x]), nullable=True
    )  # NOTE: For user messages only
    SuggestedPrompts = sa.Column(sa.UnicodeText, nullable=False, default='[]')
    SystemReplyType = sa.Column(sa.Enum(SystemReplyType, values_callable=lambda x: [e.value for e in x]), nullable=True)

    Conversation = relationship('QualConversation', back_populates='Messages')
    Documents = relationship('QualDocument', back_populates='Message')
    ProcessingMessages = relationship('QualProcessingMessage', back_populates='ConversationMessage')


Index('IX__QualConversationMessage__CreatedAt_Id', QualConversationMessage.CreatedAt, QualConversationMessage.Id)
