import logging

import azure.durable_functions as df
from azure.durable_functions import DurableOrchestrationContext

from constants.durable_functions import ActivityName, EventType, OrchestratorName
from constants.extracted_data import DataSourceType
from durable_functions.activities.models import (
    EnhancedExtractionActivityInput,
    EnhancedExtractionActivityOutput,
    FieldExtractionTask,
    ListBlobsActivityInput,
    ListBlobsActivityOutput,
    SaveExtractionDataActivityInput,
    SendNotificationActivityInput,
    SummarizeOtherFieldActivityInput,
    SummarizeOtherFieldActivityOutput,
)

from .models import EnhancedProcessingInput


logger = logging.getLogger(__name__)
bp = df.Blueprint()


# Constants for better maintainability
MAX_PROGRESS_PERCENT = 99
ACTIVITIES_PER_BLOB_WITH_EXTRACTION = 3
ACTIVITIES_PER_BLOB_WITHOUT_EXTRACTION = 1


def _create_extraction_tasks(summarized_content: str) -> list[FieldExtractionTask]:
    """Create a list of field extraction tasks based on summarized content.

    Args:
        summarized_content: The summarized content to extract fields from

    Returns:
        List of field extraction tasks for engagement_summary and one_line_description
    """
    return [
        FieldExtractionTask(
            field_name='engagement_summary',
            context=summarized_content,
            system_prompt='Extract the engagement_summary from the context.',
            user_prompt=f'Context: {summarized_content}',
        ),
        FieldExtractionTask(
            field_name='one_line_description',
            context=summarized_content,
            system_prompt='Extract the one_line_description from the context.',
            user_prompt=f'Context: {summarized_content}',
        ),
    ]


def _create_progress_notification_input(
    signalr_user_id: str, message_id: str, progress_percent: int
) -> SendNotificationActivityInput:
    """Create a progress notification input for SignalR.

    Args:
        signalr_user_id: The SignalR user ID to send notification to
        message_id: The message ID being processed
        progress_percent: The progress percentage (0-100)

    Returns:
        SendNotificationActivityInput for the progress notification
    """
    return SendNotificationActivityInput(
        event_type=EventType.DraftQualProgress,
        data={'percent': progress_percent, 'message_id': message_id},
        signalr_user_id=signalr_user_id,
    )


def _calculate_progress_percent(
    blob_progress_start: float, activity_progress_increment: float, activity_number: int
) -> int:
    """Calculate progress percentage for a specific activity.

    Args:
        blob_progress_start: Starting progress for this blob
        activity_progress_increment: Progress increment per activity
        activity_number: Current activity number (1-based)

    Returns:
        Progress percentage capped at MAX_PROGRESS_PERCENT
    """
    current_progress = blob_progress_start + (activity_progress_increment * activity_number)
    return min(int(round(current_progress)), MAX_PROGRESS_PERCENT)


@bp.orchestration_trigger('context', OrchestratorName.EnchancedProcessingSubOrchestrator)
def process_message(context: DurableOrchestrationContext):
    """Orchestrate the enhanced processing for a single message ID.

    This function processes a single message by:
    1. Listing blobs for the message
    2. Processing each blob through summarization
    3. Optionally performing enhanced extraction and data saving
    4. Sending progress notifications throughout the process

    Args:
        context: The durable orchestration context containing input data

    Returns:
        None - This is a generator function that yields activities
    """
    input_dict = context.get_input()
    if not input_dict:
        logger.error('No input provided for enhanced processing sub-orchestrator')
        return

    # Validate required input fields
    try:
        message_id = input_dict['message_id']
        orch_name = input_dict['orch_name']
        tense = input_dict['tense']
        signalr_user_id = input_dict.get('signalr_user_id')
        total_count = input_dict.get('total_count')
    except KeyError as e:
        logger.error(f'Missing required input field: {e}')
        return

    # Note: 0% progress is now sent from the main orchestrator

    prefix = f'chunks_extracted/{message_id}/'.lower()
    blobs_outputs: list[ListBlobsActivityOutput] = yield context.call_activity(
        ActivityName.ListBlobs, ListBlobsActivityInput(prefix=prefix)
    )
    logger.info(f'Found {len(blobs_outputs)} blobs for message_id {message_id}')
    logger.info(f'Tense: {tense}')

    # Calculate total events for progress tracking
    # Count actual processing steps that will occur
    processing_blobs = [blob for blob in blobs_outputs if blob.chunks]

    # Each processing blob will have at minimum 1 activity (SummarizeOtherField)
    # Additional activities depend on whether summarized content is produced
    # We'll calculate progress dynamically based on actual execution
    total_blobs = len(processing_blobs)
    if total_blobs == 0:
        # No processing to do, return early (100% will be sent from main orchestrator)
        return

    # Use a base increment per blob, will adjust based on actual activities
    base_progress_per_blob = MAX_PROGRESS_PERCENT / total_count * total_blobs
    processed_blobs = 0

    for blob_output in blobs_outputs:
        chunks_extracted = blob_output.chunks
        source = blob_output.source

        if not chunks_extracted:
            continue

        # Calculate progress for this blob (will be divided among its activities)
        blob_progress_start = processed_blobs * base_progress_per_blob

        summarize_input = SummarizeOtherFieldActivityInput(
            conversation_id=message_id, chunks_extracted=chunks_extracted
        )
        summarized_result: SummarizeOtherFieldActivityOutput = yield context.call_activity(
            ActivityName.SummarizeOtherField, summarize_input
        )
        logger.info(f'Summarized other field for message_id {message_id}: {summarized_result}')

        # Determine how many activities this blob will have
        will_have_enhanced_extraction = summarized_result and summarized_result.summarized_other_content
        activities_for_this_blob = (
            ACTIVITIES_PER_BLOB_WITH_EXTRACTION
            if will_have_enhanced_extraction
            else ACTIVITIES_PER_BLOB_WITHOUT_EXTRACTION
        )
        activity_progress_increment = base_progress_per_blob / activities_for_this_blob

        # Update progress after SummarizeOtherField completion (1st activity)
        if signalr_user_id:
            progress_percent = _calculate_progress_percent(blob_progress_start, activity_progress_increment, 1)
            notification_input = _create_progress_notification_input(signalr_user_id, message_id, progress_percent)
            yield context.call_activity(ActivityName.SendNotification, notification_input)

        if will_have_enhanced_extraction and summarized_result.summarized_other_content:
            tasks = _create_extraction_tasks(summarized_result.summarized_other_content)
            enhanced_extraction_input = EnhancedExtractionActivityInput(
                conversation_id=message_id, tasks=tasks, source=source
            )
            enhanced_extraction_result: EnhancedExtractionActivityOutput = yield context.call_activity(
                ActivityName.EnhancedExtraction, enhanced_extraction_input
            )
            logger.info(
                f'[{orch_name}] Enhanced extraction for source "{source}" completed '
                f'for message_id {message_id}: {enhanced_extraction_result.extracted_data}'
            )

            # Update progress after EnhancedExtraction completion (2nd activity)
            if signalr_user_id:
                progress_percent = _calculate_progress_percent(blob_progress_start, activity_progress_increment, 2)
                notification_input = _create_progress_notification_input(signalr_user_id, message_id, progress_percent)
                yield context.call_activity(ActivityName.SendNotification, notification_input)

            if enhanced_extraction_result and enhanced_extraction_result.extracted_data:
                save_extraction_input = SaveExtractionDataActivityInput(
                    message_id=message_id,
                    extraction_data=enhanced_extraction_result.extracted_data,
                    data_source_type=DataSourceType(source),
                )
                yield context.call_activity(ActivityName.SaveExtractionData, save_extraction_input)

            # Update progress after SaveExtractionData completion (3rd activity)
            if signalr_user_id:
                progress_percent = _calculate_progress_percent(blob_progress_start, activity_progress_increment, 3)
                notification_input = _create_progress_notification_input(signalr_user_id, message_id, progress_percent)
                yield context.call_activity(ActivityName.SendNotification, notification_input)

        # Mark this blob as processed
        processed_blobs += 1

    # Note: 100% progress is now sent from the main orchestrator


@bp.orchestration_trigger(context_name='context', orchestration=OrchestratorName.EnchancedExtraction)
def enchanced_processing_orchestrator(context: DurableOrchestrationContext):
    """Main orchestrator for enhanced data processing.

    This orchestrator implements a fan-out/fan-in pattern to process multiple
    message IDs in parallel. For each message, it:
    1. Sends 0% progress notification
    2. Calls sub-orchestrator for processing
    3. Sends 100% progress notification after completion

    The sub-orchestrators handle the actual data extraction, summarization,
    and enhanced extraction activities.

    Args:
        context: The durable orchestration context containing input data

    Returns:
        None - This is a generator function that yields activities
    """
    orch_name = str(OrchestratorName.EnchancedExtraction)
    input_dict = context.get_input()

    if not input_dict:
        logger.error(f'No input provided for {orch_name}')
        return

    try:
        input_data = EnhancedProcessingInput.model_validate(input_dict)
    except Exception as e:
        logger.error(f'Invalid input data for {orch_name}: {e}')
        return

    if not input_data.message_ids:
        logger.error(f'No message IDs provided for {orch_name}')
        return

    try:
        # Send 0% progress for all message IDs before starting processing
        if input_data.signalr_user_id:
            for message_id in input_data.message_ids:
                notification_input = _create_progress_notification_input(input_data.signalr_user_id, message_id, 0)
                yield context.call_activity(ActivityName.SendNotification, notification_input)

        processing_tasks = [
            context.call_sub_orchestrator(
                OrchestratorName.EnchancedProcessingSubOrchestrator,
                {
                    'message_id': message_id,
                    'orch_name': orch_name,
                    'tense': input_data.tense,
                    'signalr_user_id': input_data.signalr_user_id,
                    'total_count': len(input_data.message_ids),
                },
            )
            for message_id in input_data.message_ids
        ]
        yield context.task_all(processing_tasks)

        # Send 100% progress for all message IDs after processing completes
        if input_data.signalr_user_id:
            for message_id in input_data.message_ids:
                notification_input = _create_progress_notification_input(input_data.signalr_user_id, message_id, 100)
                yield context.call_activity(ActivityName.SendNotification, notification_input)

        logger.info(f'Successfully completed {orch_name} for all message IDs.')

    except Exception:
        logger.exception(f'Error in {orch_name} orchestrator')
