import logging

import azure.durable_functions as df
from azure.durable_functions import DurableOrchestrationContext

from constants.durable_functions import ActivityName, EventType, OrchestratorName
from constants.extracted_data import DataSourceType
from durable_functions.activities.models import (
    EnhancedExtractionActivityInput,
    EnhancedExtractionActivityOutput,
    FieldExtractionTask,
    ListBlobsActivityInput,
    ListBlobsActivityOutput,
    SaveExtractionDataActivityInput,
    SendNotificationActivityInput,
    SummarizeOtherFieldActivityInput,
    SummarizeOtherFieldActivityOutput,
)

from .models import EnhancedProcessingInput


logger = logging.getLogger(__name__)
bp = df.Blueprint()


def _create_extraction_tasks(summarized_content: str) -> list[FieldExtractionTask]:
    """Create a list of field extraction tasks based on summarized content."""
    return [
        FieldExtractionTask(
            field_name='engagement_summary',
            context=summarized_content,
            system_prompt='Extract the engagement_summary from the context.',
            user_prompt=f'Context: {summarized_content}',
        ),
        FieldExtractionTask(
            field_name='one_line_description',
            context=summarized_content,
            system_prompt='Extract the one_line_description from the context.',
            user_prompt=f'Context: {summarized_content}',
        ),
    ]


@bp.orchestration_trigger('context', OrchestratorName.EnchancedProcessingSubOrchestrator)
def process_message(context: DurableOrchestrationContext):
    """Orchestrate the enhanced processing for a single message ID."""
    input_dict = context.get_input()
    if not input_dict:
        logger.error('No input provided for enhanced processing orchestrator')
        return
    message_id = input_dict['message_id']
    orch_name = input_dict['orch_name']
    tense = input_dict['tense']
    signalr_user_id = input_dict.get('signalr_user_id')

    # Send 0% progress at initialization
    if signalr_user_id:
        yield context.call_activity(
            ActivityName.SendNotification,
            SendNotificationActivityInput(
                event_type=EventType.DraftQualProgress,
                data={'percent': 0, 'message_id': message_id},
                signalr_user_id=signalr_user_id,
            ),
        )

    prefix = f'chunks_extracted/{message_id}/'.lower()
    blobs_outputs: list[ListBlobsActivityOutput] = yield context.call_activity(
        ActivityName.ListBlobs, ListBlobsActivityInput(prefix=prefix)
    )
    logger.info(f'Found {len(blobs_outputs)} blobs for message_id {message_id}')
    logger.info(f'Tense: {tense}')

    # Calculate total events for progress tracking
    # Count actual processing steps that will occur
    processing_blobs = [blob for blob in blobs_outputs if blob.chunks]

    # Each processing blob will have at minimum 1 activity (SummarizeOtherField)
    # Additional activities depend on whether summarized content is produced
    # We'll calculate progress dynamically based on actual execution
    total_blobs = len(processing_blobs)
    if total_blobs == 0:
        # No processing to do, send 100% immediately
        if signalr_user_id:
            yield context.call_activity(
                ActivityName.SendNotification,
                SendNotificationActivityInput(
                    event_type=EventType.DraftQualProgress,
                    data={'percent': 100, 'message_id': message_id},
                    signalr_user_id=signalr_user_id,
                ),
            )
        return

    # Use a base increment per blob, will adjust based on actual activities
    base_progress_per_blob = 99 / total_blobs
    current_progress = 0
    processed_blobs = 0

    for blob_output in blobs_outputs:
        chunks_extracted = blob_output.chunks
        source = blob_output.source

        if not chunks_extracted:
            continue

        # Calculate progress for this blob (will be divided among its activities)
        blob_progress_start = processed_blobs * base_progress_per_blob

        summarize_input = SummarizeOtherFieldActivityInput(
            conversation_id=message_id, chunks_extracted=chunks_extracted
        )
        summarized_result: SummarizeOtherFieldActivityOutput = yield context.call_activity(
            ActivityName.SummarizeOtherField, summarize_input
        )
        logger.info(f'Summarized other field for message_id {message_id}: {summarized_result}')

        # Determine how many activities this blob will have
        will_have_enhanced_extraction = summarized_result and summarized_result.summarized_other_content
        activities_for_this_blob = 3 if will_have_enhanced_extraction else 1
        activity_progress_increment = base_progress_per_blob / activities_for_this_blob

        # Update progress after SummarizeOtherField completion (1st activity)
        if signalr_user_id:
            current_progress = blob_progress_start + activity_progress_increment
            progress_percent = min(int(round(current_progress)), 99)
            yield context.call_activity(
                ActivityName.SendNotification,
                SendNotificationActivityInput(
                    event_type=EventType.DraftQualProgress,
                    data={'percent': progress_percent, 'message_id': message_id},
                    signalr_user_id=signalr_user_id,
                ),
            )

        if will_have_enhanced_extraction and summarized_result.summarized_other_content:
            tasks = _create_extraction_tasks(summarized_result.summarized_other_content)
            enhanced_extraction_input = EnhancedExtractionActivityInput(
                conversation_id=message_id, tasks=tasks, source=source
            )
            enhanced_extraction_result: EnhancedExtractionActivityOutput = yield context.call_activity(
                ActivityName.EnhancedExtraction, enhanced_extraction_input
            )
            logger.info(
                f'[{orch_name}] Enhanced extraction for source "{source}" completed '
                f'for message_id {message_id}: {enhanced_extraction_result.extracted_data}'
            )

            # Update progress after EnhancedExtraction completion (2nd activity)
            if signalr_user_id:
                current_progress = blob_progress_start + (activity_progress_increment * 2)
                progress_percent = min(int(round(current_progress)), 99)
                yield context.call_activity(
                    ActivityName.SendNotification,
                    SendNotificationActivityInput(
                        event_type=EventType.DraftQualProgress,
                        data={'percent': progress_percent, 'message_id': message_id},
                        signalr_user_id=signalr_user_id,
                    ),
                )

            if enhanced_extraction_result and enhanced_extraction_result.extracted_data:
                save_extraction_input = SaveExtractionDataActivityInput(
                    message_id=message_id,
                    extraction_data=enhanced_extraction_result.extracted_data,
                    data_source_type=DataSourceType(source),
                )
                yield context.call_activity(ActivityName.SaveExtractionData, save_extraction_input)

            # Update progress after SaveExtractionData completion (3rd activity)
            if signalr_user_id:
                current_progress = blob_progress_start + (activity_progress_increment * 3)
                progress_percent = min(int(round(current_progress)), 99)
                yield context.call_activity(
                    ActivityName.SendNotification,
                    SendNotificationActivityInput(
                        event_type=EventType.DraftQualProgress,
                        data={'percent': progress_percent, 'message_id': message_id},
                        signalr_user_id=signalr_user_id,
                    ),
                )

        # Mark this blob as processed
        processed_blobs += 1

    # Send 100% progress after all processing is complete
    if signalr_user_id:
        yield context.call_activity(
            ActivityName.SendNotification,
            SendNotificationActivityInput(
                event_type=EventType.DraftQualProgress,
                data={'percent': 100, 'message_id': message_id},
                signalr_user_id=signalr_user_id,
            ),
        )


@bp.orchestration_trigger(context_name='context', orchestration=OrchestratorName.EnchancedExtraction)
def enchanced_processing_orchestrator(context: DurableOrchestrationContext):
    """
    Main orchestrator for enhanced data processing.

    This orchestrator fans out to process multiple message IDs in parallel.
    For each message, it extracts data from different sources, summarizes it,
    and performs enhanced extraction.
    """
    orch_name = str(OrchestratorName.EnchancedExtraction)
    input_dict = context.get_input()
    try:
        input_data = EnhancedProcessingInput.model_validate(input_dict)

        if not input_data.message_ids:
            logger.error(f'No message IDs provided for {orch_name}')
            return

        processing_tasks = [
            context.call_sub_orchestrator(
                OrchestratorName.EnchancedProcessingSubOrchestrator,
                {
                    'message_id': message_id,
                    'orch_name': orch_name,
                    'tense': input_data.tense,
                    'signalr_user_id': input_data.signalr_user_id
                },
            )
            for message_id in input_data.message_ids
        ]
        yield context.task_all(processing_tasks)

        logger.info(f'Successfully completed {orch_name} for all message IDs.')
        # Send 100% progress after all processing is complete
        yield context.call_activity(
            ActivityName.SendNotification,
            SendNotificationActivityInput(
                event_type=EventType.DraftQualProgress,
                data={'percent': 100},
                signalr_user_id=input_data.signalr_user_id,
            ),
        )

    except Exception:
        logger.exception(f'Error in {orch_name} orchestrator')
